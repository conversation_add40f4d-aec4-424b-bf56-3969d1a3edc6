# Android Development Environment Setup

This repository contains an automated setup script for the latest Android development environment as of August 2025.

## Target Specifications

- **Android Studio**: 2025.1.2 (Narwhal Feature Drop)
- **Android Gradle Plugin (AGP)**: 8.11.1
- **Kotlin**: 2.1.0
- **Kotlin Gradle Plugin**: 2.1.0
- **K<PERSON> (Kotlin Symbol Processing)**: 2.1.0-1.0.29
- **JVM Target**: 17
- **Compile SDK**: 35
- **Target SDK**: 35
- **Min SDK**: 24 (Android 7.0)
- **Gradle**: 8.11.1

## Prerequisites

### System Requirements

#### Minimum Hardware Requirements
- **RAM**: 8 GB (16 GB recommended)
- **Disk Space**: 4 GB for Android Studio + 2 GB for Android SDK
- **Screen Resolution**: 1280 x 800 minimum

#### Operating System Support
- **Windows**: Windows 10/11 (64-bit)
- **macOS**: macOS 10.14 (API level 28) or higher
- **Linux**: Any 64-bit Linux distribution that supports Gnome, KDE, or Unity DE

### Required Tools
- `curl` or `wget` for downloads
- `unzip` for extracting archives
- Package manager for your OS (apt, yum, dnf, pacman, brew, etc.)

## Quick Start

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. **Make the script executable**
   ```bash
   chmod +x setup.sh
   ```

3. **Run the setup script**
   ```bash
   ./setup.sh
   ```

4. **Restart your terminal or reload environment**
   ```bash
   source ~/.bashrc  # or ~/.zshrc for zsh users
   ```

## What the Script Does

### 1. JDK 17 Installation
- Detects your operating system
- Installs OpenJDK 17 using the appropriate package manager
- Sets `JAVA_HOME` environment variable
- Updates `PATH` to include Java binaries

### 2. Android SDK Command Line Tools
- Downloads the latest Android SDK Command Line Tools
- Extracts to `~/Android/Sdk`
- Sets `ANDROID_HOME` environment variable
- Updates `PATH` to include SDK tools

### 3. Android SDK Components
- Accepts SDK licenses automatically
- Installs essential SDK components:
  - Platform Tools (adb, fastboot)
  - Android API 35 (latest)
  - Android API 34 (backup)
  - Build Tools 35.0.0 and 34.0.0
  - Android Emulator
  - System images for emulator

### 4. Android Studio Download
- Downloads Android Studio Narwhal Feature Drop 2025.1.2
- Provides installation instructions for each platform

### 5. Gradle Optimization
- Creates optimized `gradle.properties` file
- Configures JVM settings for better performance
- Enables parallel builds and caching

## Manual Steps After Script Completion

### 1. Install Android Studio
- **Linux**: Extract the downloaded tar.gz and run `./android-studio/bin/studio.sh`
- **macOS**: Install the downloaded DMG file
- **Windows**: Run the downloaded installer

### 2. Complete Android Studio Setup
1. Launch Android Studio
2. Follow the setup wizard
3. Accept licenses
4. Download additional SDK components if prompted

### 3. Create Your First Project
Use these settings when creating a new project:
```gradle
// In app/build.gradle.kts
android {
    compileSdk = 35
    
    defaultConfig {
        targetSdk = 35
        minSdk = 24
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = "17"
    }
}

// In build.gradle.kts (Project level)
plugins {
    id("com.android.application") version "8.11.1" apply false
    id("org.jetbrains.kotlin.android") version "2.1.0" apply false
    id("com.google.devtools.ksp") version "2.1.0-1.0.29" apply false
}
```

## Troubleshooting

### Common Issues

#### 1. Permission Denied
```bash
chmod +x setup.sh
```

#### 2. Java Not Found After Installation
```bash
source ~/.bashrc
# or
export JAVA_HOME="/path/to/jdk17"
export PATH="$JAVA_HOME/bin:$PATH"
```

#### 3. Android SDK License Issues
```bash
yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses
```

#### 4. Gradle Build Issues
- Ensure `JAVA_HOME` points to JDK 17
- Check `gradle.properties` file exists in `~/.gradle/`
- Clear Gradle cache: `rm -rf ~/.gradle/caches/`

### Platform-Specific Issues

#### Windows
- Use Git Bash or WSL for running the script
- Manually set environment variables in System Properties if needed
- Install JDK 17 using: `winget install EclipseAdoptium.Temurin.17.JDK`

#### macOS
- Install Homebrew first: `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`
- Grant necessary permissions for downloads and installations

#### Linux
- Ensure your package manager is up to date
- Install `curl` or `wget` if not available
- Some distributions may require additional dependencies

## Verification

After setup completion, verify your installation:

```bash
# Check Java version
java -version
# Should show: openjdk version "17.x.x"

# Check Android SDK
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --list

# Check environment variables
echo $JAVA_HOME
echo $ANDROID_HOME

# Create a test AVD
$ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd \
  -n test_avd \
  -k "system-images;android-35;google_apis;x86_64" \
  --device "pixel_7"
```

## Updates and Maintenance

### Updating SDK Components
```bash
$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --update
```

### Updating Android Studio
- Check for updates within Android Studio: Help → Check for Updates
- Or download the latest version from the official website

### Updating Gradle and Dependencies
- Update versions in your project's `build.gradle.kts` files
- Sync project after changes

## Additional Resources

- [Android Studio User Guide](https://developer.android.com/studio/intro)
- [Android Gradle Plugin Release Notes](https://developer.android.com/build/releases/gradle-plugin)
- [Kotlin Release Notes](https://kotlinlang.org/docs/releases.html)
- [Android API Levels](https://developer.android.com/guide/topics/manifest/uses-sdk-element#ApiLevels)

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify system requirements
3. Check official documentation links
4. Ensure you have the latest version of this script

## License

This setup script is provided as-is for educational and development purposes.
