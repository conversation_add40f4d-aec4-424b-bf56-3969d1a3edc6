#!/bin/bash

# Android Development Environment Setup Script
# Updated for Android Studio 2025.1.2 (Narwhal Feature Drop)
# 
# Specifications:
# - Android Studio: 2025.1.2 (Narwhal Feature Drop)
# - Android Gradle Plugin (AGP): 8.11.1
# - Kotlin: 2.1.0
# - Kotlin Gradle Plugin: 2.1.0
# - KSP (Kotlin Symbol Processing): 2.1.0-1.0.29
# - JVM Target: 17
# - Compile SDK: 35
# - Target SDK: 35
# - Min SDK: 24 (Android 7.0)
# - Gradle: 8.11.1

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
    else
        log_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    log_info "Detected OS: $OS"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Download file with progress
download_file() {
    local url="$1"
    local output="$2"
    
    if command_exists curl; then
        curl -L --progress-bar "$url" -o "$output"
    elif command_exists wget; then
        wget --progress=bar:force "$url" -O "$output"
    else
        log_error "Neither curl nor wget found. Please install one of them."
        exit 1
    fi
}

# Install JDK 17
install_jdk17() {
    log_info "Installing JDK 17..."
    
    if command_exists java; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
        if [[ "$JAVA_VERSION" == "17" ]]; then
            log_success "JDK 17 is already installed"
            return
        fi
    fi
    
    case $OS in
        "linux")
            if command_exists apt-get; then
                # Ubuntu/Debian
                sudo apt-get update
                sudo apt-get install -y openjdk-17-jdk
            elif command_exists yum; then
                # CentOS/RHEL/Fedora
                sudo yum install -y java-17-openjdk-devel
            elif command_exists dnf; then
                # Fedora (newer)
                sudo dnf install -y java-17-openjdk-devel
            elif command_exists pacman; then
                # Arch Linux
                sudo pacman -S --noconfirm jdk17-openjdk
            else
                log_warning "Package manager not detected. Please install JDK 17 manually."
                log_info "Download from: https://adoptium.net/temurin/releases/?version=17"
            fi
            ;;
        "macos")
            if command_exists brew; then
                brew install openjdk@17
                # Link it for system use
                sudo ln -sfn $(brew --prefix)/opt/openjdk@17/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-17.jdk
            else
                log_warning "Homebrew not found. Please install JDK 17 manually."
                log_info "Download from: https://adoptium.net/temurin/releases/?version=17"
            fi
            ;;
        "windows")
            log_warning "Please install JDK 17 manually on Windows."
            log_info "Download from: https://adoptium.net/temurin/releases/?version=17"
            log_info "Or use: winget install EclipseAdoptium.Temurin.17.JDK"
            ;;
    esac
}

# Set JAVA_HOME
set_java_home() {
    log_info "Setting JAVA_HOME..."
    
    case $OS in
        "linux")
            JAVA_HOME_PATH="/usr/lib/jvm/java-17-openjdk-amd64"
            if [[ ! -d "$JAVA_HOME_PATH" ]]; then
                JAVA_HOME_PATH="/usr/lib/jvm/java-17-openjdk"
            fi
            ;;
        "macos")
            JAVA_HOME_PATH="/Library/Java/JavaVirtualMachines/openjdk-17.jdk/Contents/Home"
            if [[ ! -d "$JAVA_HOME_PATH" ]]; then
                JAVA_HOME_PATH=$(/usr/libexec/java_home -v 17 2>/dev/null || echo "")
            fi
            ;;
        "windows")
            log_info "Please set JAVA_HOME manually to your JDK 17 installation path"
            return
            ;;
    esac
    
    if [[ -d "$JAVA_HOME_PATH" ]]; then
        export JAVA_HOME="$JAVA_HOME_PATH"
        echo "export JAVA_HOME=\"$JAVA_HOME_PATH\"" >> ~/.bashrc
        echo "export PATH=\"\$JAVA_HOME/bin:\$PATH\"" >> ~/.bashrc
        log_success "JAVA_HOME set to: $JAVA_HOME_PATH"
    else
        log_warning "Could not automatically set JAVA_HOME. Please set it manually."
    fi
}

# Install Android SDK Command Line Tools
install_android_sdk() {
    log_info "Installing Android SDK Command Line Tools..."
    
    # Create Android SDK directory
    ANDROID_HOME="$HOME/Android/Sdk"
    mkdir -p "$ANDROID_HOME"
    
    # Download URLs for command line tools
    case $OS in
        "linux")
            SDK_URL="https://dl.google.com/android/repository/commandlinetools-linux-13114758_latest.zip"
            ;;
        "macos")
            SDK_URL="https://dl.google.com/android/repository/commandlinetools-mac-13114758_latest.zip"
            ;;
        "windows")
            SDK_URL="https://dl.google.com/android/repository/commandlinetools-win-13114758_latest.zip"
            ;;
    esac
    
    # Download and extract
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    log_info "Downloading Android SDK Command Line Tools..."
    download_file "$SDK_URL" "commandlinetools.zip"
    
    log_info "Extracting Android SDK Command Line Tools..."
    unzip -q commandlinetools.zip
    
    # Move to proper location
    mkdir -p "$ANDROID_HOME/cmdline-tools"
    mv cmdline-tools "$ANDROID_HOME/cmdline-tools/latest"
    
    # Set environment variables
    export ANDROID_HOME="$ANDROID_HOME"
    export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH"
    
    # Add to shell profile
    echo "export ANDROID_HOME=\"$ANDROID_HOME\"" >> ~/.bashrc
    echo "export PATH=\"\$ANDROID_HOME/cmdline-tools/latest/bin:\$ANDROID_HOME/platform-tools:\$PATH\"" >> ~/.bashrc
    
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
    
    log_success "Android SDK Command Line Tools installed to: $ANDROID_HOME"
}

# Install Android SDK components
install_android_components() {
    log_info "Installing Android SDK components..."
    
    # Accept licenses
    yes | sdkmanager --licenses > /dev/null 2>&1 || true
    
    # Install required components
    sdkmanager "platform-tools"
    sdkmanager "platforms;android-35"  # API 35
    sdkmanager "platforms;android-34"  # API 34 (backup)
    sdkmanager "build-tools;35.0.0"
    sdkmanager "build-tools;34.0.0"    # Backup build tools
    sdkmanager "emulator"
    sdkmanager "system-images;android-35;google_apis;x86_64"
    
    log_success "Android SDK components installed"
}

# Download Android Studio
download_android_studio() {
    log_info "Downloading Android Studio Narwhal Feature Drop 2025.1.2..."
    
    case $OS in
        "linux")
            STUDIO_URL="https://redirector.gvt1.com/edgedl/android/studio/ide-zips/2025.1.2.12/android-studio-2025.1.2.12-linux.tar.gz"
            STUDIO_FILE="android-studio-linux.tar.gz"
            ;;
        "macos")
            STUDIO_URL="https://redirector.gvt1.com/edgedl/android/studio/install/2025.1.2.12/android-studio-2025.1.2.12-mac.dmg"
            STUDIO_FILE="android-studio-mac.dmg"
            ;;
        "windows")
            STUDIO_URL="https://redirector.gvt1.com/edgedl/android/studio/install/2025.1.2.12/android-studio-2025.1.2.12-windows.exe"
            STUDIO_FILE="android-studio-windows.exe"
            ;;
    esac
    
    if [[ ! -f "$STUDIO_FILE" ]]; then
        log_info "Downloading from: $STUDIO_URL"
        download_file "$STUDIO_URL" "$STUDIO_FILE"
        log_success "Android Studio downloaded: $STUDIO_FILE"
    else
        log_info "Android Studio already downloaded: $STUDIO_FILE"
    fi
    
    case $OS in
        "linux")
            log_info "Extracting Android Studio..."
            tar -xzf "$STUDIO_FILE"
            log_info "Android Studio extracted. Run: ./android-studio/bin/studio.sh"
            ;;
        "macos")
            log_info "Please install the downloaded DMG file manually"
            open "$STUDIO_FILE"
            ;;
        "windows")
            log_info "Please run the downloaded installer manually: $STUDIO_FILE"
            ;;
    esac
}

# Create gradle.properties with optimizations
create_gradle_properties() {
    log_info "Creating optimized gradle.properties..."
    
    GRADLE_DIR="$HOME/.gradle"
    mkdir -p "$GRADLE_DIR"
    
    cat > "$GRADLE_DIR/gradle.properties" << EOF
# Gradle optimization settings for Android development
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseParallelGC
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# Android specific optimizations
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
android.enableBuildCache=true

# Kotlin optimizations
kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.incremental.js=true
kotlin.caching.enabled=true
kotlin.parallel.tasks.in.project=true

# KSP optimizations
ksp.incremental=true
ksp.incremental.intermodule=true
EOF
    
    log_success "Gradle properties created at: $GRADLE_DIR/gradle.properties"
}

# Main installation function
main() {
    log_info "Starting Android Development Environment Setup"
    log_info "Target specifications:"
    log_info "  - Android Studio: 2025.1.2 (Narwhal Feature Drop)"
    log_info "  - Android Gradle Plugin: 8.11.1"
    log_info "  - Kotlin: 2.1.0"
    log_info "  - JDK: 17"
    log_info "  - Gradle: 8.11.1"
    log_info "  - Compile/Target SDK: 35"
    log_info "  - Min SDK: 24"
    echo
    
    detect_os
    install_jdk17
    set_java_home
    install_android_sdk
    install_android_components
    create_gradle_properties
    download_android_studio
    
    echo
    log_success "Android Development Environment Setup Complete!"
    log_info "Next steps:"
    log_info "1. Restart your terminal or run: source ~/.bashrc"
    log_info "2. Launch Android Studio and complete the setup wizard"
    log_info "3. Create a new project with the following settings:"
    log_info "   - Compile SDK: 35"
    log_info "   - Target SDK: 35"
    log_info "   - Min SDK: 24"
    log_info "   - Kotlin: 2.1.0"
    log_info "   - AGP: 8.11.1"
    log_info "   - Gradle: 8.11.1"
    echo
    log_info "Environment variables set:"
    log_info "  JAVA_HOME: $JAVA_HOME"
    log_info "  ANDROID_HOME: $ANDROID_HOME"
    echo
    log_info "Useful commands:"
    log_info "  - Check Java version: java -version"
    log_info "  - Check Android SDK: sdkmanager --list"
    log_info "  - Create AVD: avdmanager create avd -n test -k 'system-images;android-35;google_apis;x86_64'"
    log_info "  - List AVDs: avdmanager list avd"
    echo
    log_warning "Note: Some download URLs may change. If downloads fail, please check:"
    log_warning "  - Android Studio: https://developer.android.com/studio"
    log_warning "  - JDK 17: https://adoptium.net/temurin/releases/?version=17"
}

# Run main function
main "$@"
